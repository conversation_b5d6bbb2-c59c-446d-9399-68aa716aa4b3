import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';

import { MarkdownContent } from '@/components/blog/MarkdownContent';
import { DatabaseService } from '@/lib/database-service';
import { BlogPost, Locale } from '@/types';

interface BlogPostPageProps {
  params: {
    locale: string;
    category: string;
    slug: string;
  };
}

// 静态生成配置 - 智能预生成策略
export async function generateStaticParams() {
  try {
    // 获取所有已发布的文章进行预生成（确保SEO覆盖）
    const publishedPosts = await DatabaseService.getBlogPosts({
      limit: 100, // 增加预生成数量
      status: 'PUBLISHED',
      orderBy: 'publishedAt',
      orderDirection: 'desc',
    });

    // 按语言和分类分组，确保多语言SEO覆盖
    const params = publishedPosts.map(post => ({
      locale: post.locale,
      category: post.category,
      slug: post.slug,
    }));

    console.log(`Generated static params for ${params.length} posts`);
    return params;
  } catch (error) {
    console.error('Error generating static params:', error);
    return []; // 返回空数组，允许动态生成
  }
}

// ISR配置 - 优化的重新验证策略
export const revalidate = 3600; // 1小时重新验证（热门内容）

// 允许动态参数 - 对于新发布的文章
export const dynamicParams = true;

// 强制静态生成 - 提高SEO效果
export const dynamic = 'force-static';

export async function generateMetadata({
  params,
}: BlogPostPageProps): Promise<Metadata> {
  try {
    const post = await getPostBySlug(
      params.category,
      params.slug,
      params.locale as Locale
    );

    if (!post) {
      return {
        title: 'Post Not Found | Mystical Website',
        description: 'The requested post could not be found.',
        robots: { index: false, follow: false },
      };
    }

    const description = post.seoDescription || post.excerpt || post.title;
    const title = post.seoTitle || post.title;
    const canonicalUrl = `${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}/${params.locale}/blog/${params.category}/${params.slug}`;

    return {
      title: `${title} | Mystical Website`,
      description,
      keywords: post.keywords?.join(', '),
      authors: [{ name: 'Mystical Website' }],
      alternates: {
        canonical: canonicalUrl,
      },
      openGraph: {
        title,
        description,
        type: 'article',
        locale: params.locale,
        url: canonicalUrl,
        siteName: 'Mystical Website',
        publishedTime: post.publishedAt?.toISOString(),
        modifiedTime: post.updatedAt.toISOString(),
        authors: ['Mystical Website'],
        section: post.category,
        tags: post.tags,
        images: post.coverImage
          ? [
              {
                url: post.coverImage,
                alt: post.title,
                width: 1200,
                height: 630,
              },
            ]
          : [],
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: post.coverImage ? [post.coverImage] : [],
        site: '@mystical_website',
      },
      robots: {
        index: true,
        follow: true,
        googleBot: {
          index: true,
          follow: true,
          'max-video-preview': -1,
          'max-image-preview': 'large',
          'max-snippet': -1,
        },
      },
    };
  } catch (error) {
    console.error('Error generating metadata:', error);
    return {
      title: 'Error | Mystical Website',
      description: 'An error occurred while loading this page.',
      robots: { index: false, follow: false },
    };
  }
}

// 数据获取函数 - 添加React缓存优化
async function getPostBySlug(
  category: string,
  slug: string,
  locale: Locale
): Promise<BlogPost | null> {
  try {
    const post = await DatabaseService.getBlogPostBySlug(slug, locale);

    // 验证分类是否匹配
    if (post && post.category !== category) {
      return null;
    }

    return post;
  } catch (error) {
    console.error('Error fetching post:', error);
    throw error; // 让上层处理错误
  }
}

async function getRelatedPosts(
  postId: string,
  categorySlug: string,
  locale: Locale,
  limit: number = 3
): Promise<BlogPost[]> {
  try {
    const posts = await DatabaseService.getBlogPosts({
      locale,
      status: 'PUBLISHED',
      category: categorySlug,
      limit: limit + 1, // 多获取一个，以防包含当前文章
    });

    // 排除当前文章
    return posts.filter(post => post.id !== postId).slice(0, limit);
  } catch (error) {
    console.error('Error fetching related posts:', error);
    return [];
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const locale = params.locale as Locale;

  try {
    // 并行获取文章数据 - SSR性能优化
    const [post /* relatedPosts将在post获取后再获取 */] = await Promise.all([
      getPostBySlug(params.category, params.slug, locale),
    ]);

    if (!post) {
      notFound();
    }

    // 获取相关文章（需要post.id，所以放在这里）
    const relatedPosts = await getRelatedPosts(
      post.id,
      params.category,
      locale
    );

    // 格式化日期选项
    const dateFormatOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };

    return (
      <div className='min-h-screen bg-white dark:bg-dark-900'>
        {/* 优化的JSON-LD结构化数据 - 更完整的SEO信息 */}
        <script
          type='application/ld+json'
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'BlogPosting',
              headline: post.title,
              description: post.seoDescription || post.excerpt || post.title,
              image: {
                '@type': 'ImageObject',
                url: post.coverImage,
                width: 1200,
                height: 630,
              },
              author: {
                '@type': 'Person',
                name: 'Mystical Website',
                url: `${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}`,
              },
              publisher: {
                '@type': 'Organization',
                name: 'Mystical Website',
                logo: {
                  '@type': 'ImageObject',
                  url: `${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}/images/logo.png`,
                },
              },
              mainEntityOfPage: {
                '@type': 'WebPage',
                '@id': `${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}/${locale}/blog/${params.category}/${params.slug}`,
              },
              url: `${process.env['NEXT_PUBLIC_APP_URL'] || 'https://mystical-website.com'}/${locale}/blog/${params.category}/${params.slug}`,
              datePublished:
                post.publishedAt?.toISOString() || post.createdAt.toISOString(),
              dateModified: post.updatedAt.toISOString(),
              articleSection: post.category,
              keywords: post.keywords?.join(', ') || post.tags?.join(', '),
              wordCount: post.content.split(' ').length,
              inLanguage: locale,
              isAccessibleForFree: true,
            }),
          }}
        />

        {/* Medium风格的文章头部 - 680px最佳阅读宽度 */}
        <header className='mx-auto max-w-[680px] px-4 pb-8 pt-16 sm:px-6 lg:px-8'>
          {/* 面包屑导航 - 优化的导航颜色 */}
          <nav className='mb-8 flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400'>
            <Link
              href={`/${locale}/blog`}
              className='transition-colors hover:text-gray-700 dark:hover:text-gray-300'
            >
              Blog
            </Link>
            <span>/</span>
            <Link
              href={`/${locale}/blog?category=${post.category}`}
              className='capitalize transition-colors hover:text-gray-700 dark:hover:text-gray-300'
            >
              {post.category}
            </Link>
          </nav>

          {/* 文章标题 - 优化的标题颜色 */}
          <h1 className='mb-6 font-serif text-4xl font-bold leading-tight tracking-tight text-gray-900 dark:text-gray-50 md:text-5xl'>
            {post.title}
          </h1>

          {/* 文章摘要 - 优化的摘要颜色 */}
          {post.excerpt && (
            <p className='mb-8 text-lg font-normal italic leading-relaxed text-gray-600 dark:text-gray-300 md:text-xl'>
              {post.excerpt}
            </p>
          )}

          {/* 作者信息和元数据 - 优化的元数据颜色 */}
          <div className='flex items-center justify-between border-b border-gray-200 py-4 dark:border-gray-700'>
            <div className='flex items-center gap-4'>
              <div className='flex h-12 w-12 items-center justify-center rounded-full border-2 border-gray-200 bg-gray-100 dark:border-gray-700 dark:bg-gray-800'>
                <span className='font-medium text-gray-600 dark:text-gray-400'>
                  A
                </span>
              </div>
              <div>
                <p className='mb-1 text-base font-semibold text-gray-900 dark:text-gray-100'>
                  Author
                </p>
                <div className='flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400'>
                  <span>
                    {new Intl.DateTimeFormat(locale, dateFormatOptions).format(
                      post.publishedAt || post.createdAt
                    )}
                  </span>
                  <span>·</span>
                  <span>{post.readingTime} min read</span>
                  <span>·</span>
                  <span className='capitalize text-gray-600 transition-colors hover:text-gray-700 dark:text-gray-300 dark:hover:text-gray-200'>
                    {post.category}
                  </span>
                </div>
              </div>
            </div>

            {/* 移除重复的互动按钮，统一在右侧浮动栏和底部处理 */}
          </div>
        </header>

        {/* 封面图片 - Medium风格，680px最佳阅读宽度 */}
        {post.coverImage && (
          <div className='mx-auto mb-12 max-w-[680px] px-4 sm:px-6 lg:px-8'>
            <Image
              src={post.coverImage}
              alt={post.title}
              width={1200}
              height={675}
              className='aspect-[16/9] w-full rounded-lg object-cover'
              priority
            />
          </div>
        )}

        {/* 主要内容区域 - Medium风格布局 */}
        <div className='relative'>
          {/* 文章内容容器 */}
          <div className='mx-auto max-w-[680px] px-4 pb-16 sm:px-6 lg:px-8'>
            {/* 文章正文 - 使用MarkdownContent组件渲染 */}
            <MarkdownContent content={post.content} />

            {/* 相关文章 */}
            {relatedPosts.length > 0 && (
              <div className='mt-16 border-t border-gray-200 pt-8 dark:border-gray-700'>
                <h3 className='mb-6 text-2xl font-bold text-gray-900 dark:text-gray-50'>
                  相关文章
                </h3>
                <div className='grid gap-6'>
                  {relatedPosts.map(relatedPost => (
                    <Link
                      key={relatedPost.id}
                      href={`/${locale}/blog/${relatedPost.category}/${relatedPost.slug}`}
                      className='group block rounded-lg border border-gray-200 p-4 transition-colors hover:border-gray-300 dark:border-gray-700 dark:hover:border-gray-600'
                    >
                      <h4 className='text-lg font-semibold text-gray-900 transition-colors group-hover:text-blue-600 dark:text-gray-100 dark:group-hover:text-blue-400'>
                        {relatedPost.title}
                      </h4>
                      {relatedPost.excerpt && (
                        <p className='mt-2 line-clamp-2 text-gray-600 dark:text-gray-400'>
                          {relatedPost.excerpt}
                        </p>
                      )}
                      <div className='mt-3 flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400'>
                        <span className='capitalize'>
                          {relatedPost.category}
                        </span>
                        <span>·</span>
                        <span>{relatedPost.readingTime} min read</span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error loading blog post:', error);
    notFound();
  }
}
